version: '2.1'
networks:
  default:
    external:
      name: localnet
      
services:
  authorization:
    build:
      context: ../../
      args:
        - COMPOSER_GITLAB_TOKEN
    ports:
      - 9028:81
    expose:
      - '81'
    environment:
      AUTH_DSN: mysql:host=host.docker.internal:13313;dbname=oauth
      AUTH_PASS: oYEZAngsWW8xy2qV
      AUTH_USER: root
      DISABLE_OPCODE_CACHE: 1
      MYSQL_DSN: mysql:host=host.docker.internal:13327;dbname=sparefoot
      MYSQL_PASS: oYEZAngsWW8xy2qV
      MYSQL_USER: root
      SF_ENV: local
      DD_TRACE_ENABLED: 'false'
      BRANCH: foo
    networks:
      default:
        aliases:
        - authorization
    restart: always
    stdin_open: true
    tty: true
    volumes:
    - /opt/sparefoot/apps/authorization:/var/www/service
