authorization:
  labels:
    sparefoot.link.name: authorization
  image: rancher/dns-service
  links:
  - authorization-{{ sf_env }}-{{ version }}

authorization-{{ sf_env }}-{{ version }}:
  image: 850077434821.dkr.ecr.us-east-1.amazonaws.com/sparefoot/envoy:k8sproxy
  labels:
    io.rancher.scheduler.affinity:host_label: sf_group=app2
    io.rancher.container.pull_image: always
  restart: always
  environment:
{% if sf_env == 'prod' %}
    K8S_HOST: k8s.sparefoot.com
    HOST_HEADER: authorization.sparefoot.com
{% elif sf_env == 'stage' %}
    K8S_HOST: k8s.sparefoot.extrameter.com
    HOST_HEADER: authorization.sparefoot.extrameter.com
{% else %}
    K8S_HOST: k8s.sparefoot.extrameter.com
    HOST_HEADER: authorization.sparefoot.moreyard.com
{% endif %}
    CLUSTER_NAME: authorization

haproxy-{{ sf_env }}:
  ports:
  - 9028:80
  expose:
  - '80:80'
  restart: always
  tty: true
  labels:
    io.rancher.scheduler.global: 'true'
    io.rancher.container.pull_image: always
    io.rancher.scheduler.affinity:host_label: sf_group=app2
  image: rancher/load-balancer-service
  links:
  - authorization-{{ sf_env }}-{{ version }}
  stdin_open: true