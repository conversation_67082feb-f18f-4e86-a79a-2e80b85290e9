# This file is a "template" of which env vars need to be defined for your application
# Copy this file to .env file for development, create environment variables when deploying to production
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=d72513b04411b7e19d20c114a6876b55
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS=localhost,example.com
###< symfony/framework-bundle ###
AUTH_DSN=mysql:host=127.0.0.1:13313;dbname=oauth
AUTH_PASS=oYEZAngsWW8xy2qV
AUTH_USER=sfadmin
BRANCH=php8
DISABLE_OPCODE_CACHE=1
MYSQL_DSN=mysql:host=127.0.0.1:13327;dbname=sparefoot
MYSQL_PASS=oYEZAngsWW8xy2qV
MYSQL_USER=sfadmin
SF_ENV=local
DD_TRACE_ENABLED='false'