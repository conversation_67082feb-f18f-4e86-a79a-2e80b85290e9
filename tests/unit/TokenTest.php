<?php

namespace Sparefoot\Authorization\Test\SDK;

use Sparefoot\Authorization\SDK\Token;

class TokenTest extends \PHPUnit\Framework\TestCase
{
    public function testDecoder()
    {
        $t = new Token();
        $t->setExpires('100');
        $t->setRefresh(md5('duckface'));
        $t->setHost('auth.localhost');
        $t->setIdentifier(md5('MrTorgue'));

        $this->assertEquals(md5('duckface'), $t->getRefresh());

        $string = $t->__toString();

        $decoded = Token::createFromRaw($string);
        $this->assertEquals(100, $decoded->getExpires());
        $this->assertEquals(false, $decoded->getRefresh());
        $this->assertEquals('auth.localhost', $decoded->getHost());
        $this->assertEquals('localhost', $decoded->getDomain());
        $this->assertEquals(md5('MrTorgue'), $decoded->getIdentifier());
    }
}
