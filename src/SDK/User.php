<?php

namespace Sparefoot\Authorization\SDK;

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/20/15
 * Time: 3:44 PM.
 */
/**
 * Class User.
 */
class User
{
    public const MYFOOT_ROLE_GOD = 'god';
    public const MYFOOT_ROLE_ADMIN = 'admin';

    private $user_id;
    private $email;
    private $first_name;
    private $last_name;
    private $account_id;
    private $restricted_to_facility_ids;
    private $myfoot_role;

    public function getUserId()
    {
        return $this->user_id;
    }

    public function getEmail()
    {
        return $this->email;
    }

    public function getFirstName()
    {
        return $this->first_name;
    }

    public function getLastName()
    {
        return $this->last_name;
    }

    public function setUserId($user_id)
    {
        $this->user_id = $user_id;
    }

    public function setEmail($email)
    {
        $this->email = $email;
    }

    public function setFirstName($first_name)
    {
        $this->first_name = $first_name;
    }

    public function setLastName($last_name)
    {
        $this->last_name = $last_name;
    }

    public function setAccountId($account_id)
    {
        $this->account_id = $account_id;
    }

    public function getAccountId()
    {
        return $this->account_id;
    }

    public function setRestrictedToFacilityIds($restricted_to_facility_ids)
    {
        $this->restricted_to_facility_ids = $restricted_to_facility_ids;
    }

    public function getRestrictedToFacilityIds()
    {
        return $this->restricted_to_facility_ids;
    }

    public function setMyfootRole($myfoot_role)
    {
        $this->myfoot_role = $myfoot_role;
    }

    public function getMyfootRole()
    {
        return $this->myfoot_role;
    }

    /**
     * @return bool
     */
    public function canManageFacility($account_id, $facility_id)
    {
        if ($this->getMyfootRole() === self::MYFOOT_ROLE_GOD) {
            return true;
        }
        if ($account_id !== $this->getAccountId()) {
            return false;
        }
        if (!$this->getRestrictedToFacilityIds()) { // unrestricted
            return true;
        }

        return in_array($facility_id, $this->getRestrictedToFacilityIds());
    }
}
