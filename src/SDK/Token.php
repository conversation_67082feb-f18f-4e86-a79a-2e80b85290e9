<?php

namespace Sparefoot\Authorization\SDK;

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/20/15
 * Time: 3:44 PM.
 */
/**
 * Class Token.
 */
class Token
{
    public const SEPARATOR = '/:~:/';
    /**
     * @var string the url that gave us the token
     */
    private $host;
    /**
     * @var string the TLD where we can use the token
     */
    private $domain;
    /**
     * @var string the persisted primary key for this token in 'storage'
     */
    private $identifier;
    /**
     * @var string the refresh token, which is similar to the identifier token
     */
    private $refresh = false;
    /**
     * @var int unix timestamp when the token expires
     */
    private $expires = 0;

    public static function createFromRaw($rawToken)
    {
        $parts = explode(self::SEPARATOR, trim($rawToken));
        if (count($parts) !== 4) {
            return false;
        }
        $token = new self();
        $token->setIdentifier($parts[0]);
        $token->setHost($parts[1]);
        $token->setRefresh(false);
        $token->setExpires($parts[3]);

        return $token;
    }

    public function getHost()
    {
        return $this->host;
    }

    public function setHost($hostValue)
    {
        $this->domain = substr($hostValue, strpos($hostValue, 'auth.') + strlen('auth.'));
        $this->host = $hostValue;
    }

    /**
     * @return string
     */
    public function getDomain()
    {
        return $this->domain;
    }

    public function getIdentifier()
    {
        return $this->identifier;
    }

    public function setIdentifier($identifierValue)
    {
        $this->identifier = $identifierValue;
    }

    public function getRefresh()
    {
        return $this->refresh;
    }

    public function setRefresh($refreshValue)
    {
        $this->refresh = $refreshValue;
    }

    public function getExpires()
    {
        return $this->expires;
    }

    public function setExpires($expiresValue)
    {
        $this->expires = $expiresValue;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->getIdentifier().self::SEPARATOR
            .$this->getHost().self::SEPARATOR
            .'reserved'.self::SEPARATOR
            .$this->getExpires();
    }
}
