<?php

namespace Sparefoot\Authorization\SDK;

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/20/15
 * Time: 3:44 PM.
 */
/**
 * Library for interacting with the SpareFoot Authorization Service.
 */
class Client implements ClientInterface
{
    /**
     * HTTP response codes from API.
     */
    public const STATUS_BAD_RESPONSE = -1; // Not an HTTP response code
    public const STATUS_OK = 200;
    public const STATUS_BAD_REQUEST = 400;
    public const STATUS_UNAUTHORIZED = 401;
    public const STATUS_FORBIDDEN = 403;
    public const STATUS_NOT_FOUND = 404;
    public const STATUS_NOT_ACCEPTABLE = 406;
    public const STATUS_INTERNAL_SERVER_ERROR = 500;
    public const STATUS_SERVICE_UNAVAILABLE = 503;

    // this is the generic client id and client secret for internal sparefoot apps
    // more specific clients with custom scopes can be created for individual apps by adding to the oauth_clients table
    public const CLIENT_ID_SPAREFOOT = 'sparefoot';
    public const CLIENT_SECRET_SPAREFOOT = '4QBYy3L2imy61TgVLpl0p09hy';

    private $api_target;
    private $client_id;
    private $client_secret;
    private $ssl_verifypeer;

    private static $_client; // our lazy loaded client

    public static function getInstance(
        $client_id = null,
        $client_secret = null,
        $api_target = null)
    {
        if (!self::$_client) { // lazy load singleton client
            if (is_null($client_id)) {
                $client_id = self::CLIENT_ID_SPAREFOOT;
            }
            if (is_null($client_secret)) {
                $client_secret = self::CLIENT_SECRET_SPAREFOOT;
            }
            if (is_null($api_target)) { // make this param easy in CI
                $api_target = self::autoUrl();
            }
            self::$_client = new self($client_id, $client_secret, $api_target);
        }

        return self::$_client;
    }

    /**
     * @param $client_id     string
     * @param $client_secret string
     * @param $api_target    string http endpoint
     */
    protected function __construct($client_id, $client_secret, $api_target)
    {
        $this->api_target = $api_target;
        $this->client_id = $client_id;
        $this->client_secret = $client_secret;
        $this->ssl_verifypeer = (strncmp('https', $this->api_target, 5) === 0);
    }

    public static function autoUrl()
    {
        if (getenv('URL_AUTHORIZATION_SERVICE') !== false) {
            return getenv('URL_AUTHORIZATION_SERVICE');
        }

        $host = gethostname();
        if (getenv('SF_ENV') !== false) {
            switch (getenv('SF_ENV')) {
                case 'prod':
                    return 'https://auth.sparefoot.com';
                case 'stage':
                    return 'https://auth.sparefoot.extrameter.com';
                case 'dev':
                    if (strpos($host, 'host-') === false) { // /wtf vagrant
                        return 'https://auth.sparefoot.moreyard.com';
                    }
                    break;
                case 'local': // rancher
                    return 'http://authorization';
                case '${env.SF_ENV}': // local vagrant
                    break;
                default:
                    throw new \Exception('SF_ENV is '.getenv('SF_ENV').' ??');
            }
        }
        if (strpos($host, '.local') !== false) { // host os
            return 'http://auth.sparefoot.localhost:8888';
        }
        if (strpos($host, 'host-') !== false) { // vagrant
            return 'http://auth.sparefoot.localhost';
        }
        if (strpos($host, 'vagrant') !== false) {
            return 'http://auth.sparefoot.localhost';
        }

        throw new \Exception('unable to determine testing endpoint for host '.$host);
    }

    /**
     * @return Token
     *
     * @throws AuthorizationException
     * @throws \Exception
     */
    public function getAuthTokenFromUserCredentials($email, $password)
    {
        $params = [
            'grant_type' => 'password',
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'username' => $email,
            'password' => $password,
        ];

        try {
            $response = $this->make_request('token', $params, 'POST');

            if (property_exists($response, 'errors')) {
                throw new \Exception($response->errors[0]->title.': '.$response->errors[0]->detail);
            }
        } catch (AuthorizationException $e) {
            if ($e->getCode() === self::STATUS_UNAUTHORIZED) {
                throw new \Exception('Invalid login on '.$this->api_target);
            } else {
                throw $e;
            }
        }

        $token = new Token();

        $token->setHost($this->api_target);
        $token->setIdentifier($response->access_token);
        $token->setRefresh($response->refresh_token);
        $token->setExpires(time() + $response->expires_in);

        return $token;
    }

    /**
     * @param $refresh string
     *
     * @return Token|false
     *
     * @throws AuthorizationException
     */
    public function refreshAuthToken($refresh)
    {
        if (!$refresh) {
            throw new \InvalidArgumentException('refresh string is required');
        }
        $params = [
            'grant_type' => 'refresh_token',
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'refresh_token' => $refresh,
        ];
        try {
            $response = $this->make_request('token', $params, 'POST');
        } catch (AuthorizationException $e) {
            throw $e;
        }

        $token = new Token();
        $token->setHost($this->api_target);
        $token->setIdentifier($response->access_token);
        $token->setRefresh($response->refresh_token);
        $token->setExpires(time() + $response->expires_in);

        return $token;
    }

    public function getAuthCodeFromRedirectUriAndSessionId($redirectUri, $sessionId)
    {
        $params = [
            'response_type' => 'code',
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'redirect_uri' => $redirectUri,
            'state' => $sessionId,
        ];

        $this->redirect_request('authorize', $params);

        // redirect stops further execution. will pickup when redirect_url is hit, which should then call getAuthTokenFromAuthCodeRedirectionParams()
    }

    public function getAuthTokenFromAuthCodeRedirectionParams($redirectUri, $sessionId)
    {
        // if the token is being returned, return it immediately, if not redirect one more time to exchange code for token
        $authToken = $this->getParam('token');
        if ($authToken) {
            return $authToken;
        }

        // the user denied the authorization request
        if (!$code = $this->getParam('code')) {
            throw new \Exception('Invalid authorization code');
        }

        // verify the "state" parameter matches this user's session (this is like CSRF - very important!!)
        if ($this->getParam('state') != $sessionId) {
            throw new \Exception('Your session has expired');
        }

        $params = [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'redirect_uri' => $redirectUri,
        ];

        $this->redirect_request('token', $params, 'POST');

        // redirection ends execution. upon redirect we should get sent back here again
    }

    /**
     * @param $rawAuthToken string raw token
     *
     * @return User|false
     */
    public static function getUserFromAuthToken($rawAuthToken)
    {
        $token = Token::createFromRaw($rawAuthToken);
        if ($token === false) {
            return false;
        }
        $client = self::getInstance(null, null, $token->getHost());

        $response = $client->make_request('resource', ['access_token' => $token->getIdentifier()], 'GET');
        if (!$response) {
            return false;
        }

        $user = new User();
        $user->setUserId($response->user_id);
        $user->setEmail($response->email);
        $user->setFirstName($response->first_name);
        $user->setLastName($response->last_name);
        $user->setAccountId($response->account_id);
        $user->setMyfootRole($response->myfoot_role);
        if (!empty($response->restricted_to_facility_ids)) {
            $user->setRestrictedToFacilityIds(explode(',', $response->restricted_to_facility_ids));
        }

        return $user;
    }

    // ///////////////////////////////////////////////////////////////////////////
    // Helper functions
    // ///////////////////////////////////////////////////////////////////////////

    /**
     * Performs a curl request.
     *
     * @param $url       string
     * @param $post_data array
     *
     * @throws AuthorizationException
     */
    private function curl_request($url, $post_data = null)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->ssl_verifypeer);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        if (is_array($post_data)) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        }
        $response = curl_exec($ch);

        // make sure we got a real response
        if (strlen($response) === 0) {
            $number = curl_errno($ch);
            $error = curl_error($ch);
            throw new AuthorizationException("CURL error: $number - $error - $url", self::STATUS_BAD_RESPONSE);
        }

        // make sure we got a 200
        $code = (int) curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($code !== self::STATUS_OK) {
            throw new AuthorizationException("HTTP status code: $code, response=$response, url=$url", $code);
        }

        curl_close($ch);

        return $response;
    }

    /**
     * @param array  $args
     * @param string $http_method
     *
     * @throws AuthorizationException
     */
    private function make_request($api_method, $args = [], $http_method = 'POST')
    {
        $url = "$this->api_target/$api_method";

        // add args to url for GET
        if ($http_method === 'GET') {
            $url .= '?'.http_build_query($args);
            $post_data = null;
        } else {
            $post_data = $args;
        }

        $rawResponse = $this->curl_request($url, $post_data);

        // make sure response is valid json
        $response = json_decode($rawResponse);

        if ($response === null) {
            throw new AuthorizationException("Invalid JSON received: $rawResponse, url=$url", self::STATUS_BAD_RESPONSE);
        }

        return $response;
    }

    private function redirect_request($api_method, $args = [], $http_method = 'GET', ?array $headers = null)
    {
        $url = "$this->api_target/$api_method";

        $params = [];

        if ($http_method === 'GET') {
            // add args to url for GET
            $url .= '?'.http_build_query($args);
        } else {
            // add args to headers for POST
            $params['http'] = [
                'method' => 'POST',
                'content' => http_build_query($args),
            ];
        }

        if (!is_null($headers)) {
            $params['http']['header'] = '';
            foreach ($headers as $k => $v) {
                $params['http']['header'] .= "$k: $v\n";
            }
        }

        $ctx = stream_context_create($params);
        $fp = @fopen($url, 'rb', false, $ctx);
        if ($fp) {
            echo @stream_get_contents($fp);
            exit;
        } else {
            // Error
            throw new \Exception("Error loading '$url', $php_errormsg");
        }
    }

    private function getParam($paramName, $default = null)
    {
        if (isset($_REQUEST[$paramName])) {
            return $_REQUEST[$paramName];
        } else {
            return $default;
        }
    }
}
