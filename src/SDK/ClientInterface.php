<?php

namespace Sparefoot\Authorization\SDK;

/**
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/20/15
 * Time: 3:44 PM.
 */
/**
 * Interface ClientInterface.
 */
interface ClientInterface
{
    /**
     * constructor.
     */
    public static function getInstance();

    /**
     * @return Token auth token
     */
    public function getAuthTokenFromUserCredentials($email, $password);

    /**
     * @return User
     */
    public static function getUserFromAuthToken($rawAuthToken);
}
