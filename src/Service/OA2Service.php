<?php

namespace Sparefoot\Authorization\Service;

use OAuth2\GrantType\AuthorizationCode;
use OAuth2\GrantType\RefreshToken;
use OAuth2\GrantType\UserCredentials;
use OAuth2\HttpFoundationBridge\Request as BridgeRequest;
use OAuth2\HttpFoundationBridge\Response as BridgeResponse;
use OAuth2\Server as OAuth2Server;
use OAuth2\Storage\Pdo;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class OA2Service
{
    private $pdoAuth;
    private $userCredentialsService;

    public function __construct(\PDO $pdoAuth, UserCredentialsService $userCredentialsService)
    {
        $this->pdoAuth = $pdoAuth;
        $this->userCredentialsService = $userCredentialsService;
    }

    public function tokenRequest()
    {
        return $this->getServer()->handleTokenRequest(\OAuth2\Request::createFromGlobals(), new BridgeResponse());
    }

    public function resourceRequest(Request $request)
    {
        $request = BridgeRequest::createFromRequest($request);

        $server = $this->getServer();

        $response = new BridgeResponse();

        if (!$server->verifyResourceRequest($request, $response)) {
            return $server->getResponse();
        } else {
            $token = $server->getAccessTokenData(\OAuth2\Request::createFromGlobals());
            $userId = $token['user_id'];

            $api_response = $this->userCredentialsService->getUserDetails($userId);
            if (!$api_response) {
                $api_response = [
                    'user_id' => $userId,
                    'error' => 'wtf?',
                ];
            }

            return new Response(json_encode($api_response));
        }
    }

    public function getServer()
    {
        $tokenStorage = new Pdo($this->pdoAuth);

        // create array of supported grant types
        $grantTypes = [
            'authorization_code' => new AuthorizationCode($tokenStorage),
            'user_credentials' => new UserCredentials($this->userCredentialsService),
            'refresh_token' => new RefreshToken($tokenStorage, [
                'always_issue_new_refresh_token' => true,
            ]),
        ];

        // server config
        $serverConfigOptions = [
            'enforce_state' => true,
            'allow_implicit' => true,
            'access_lifetime' => 1209600, // temporary 14 day token expiration
        ];

        // instantiate the oauth server
        return new OAuth2Server($tokenStorage, $serverConfigOptions, $grantTypes);
    }
}
