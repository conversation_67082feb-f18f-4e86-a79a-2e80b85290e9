<?php

namespace Sparefoot\Authorization\Service;

use OAuth2\Storage\UserCredentialsInterface;

/**
 * Implements the OAuth2 UserCredentials interface.
 *
 * <AUTHOR> Chamberlain
 */
class UserCredentialsService implements UserCredentialsInterface
{
    private $pdo;

    public function __construct(\PDO $pdoMaster)
    {
        $this->pdo = $pdoMaster;
    }

    /**
     * @return bool
     */
    public function checkUserCredentials($email, $password)
    {
        if ($this->pdo === null) {
            throw new \InvalidArgumentException('injecting app failed');
        }
        $statement = $this->pdo->prepare('select password, bcrypt_password_flag from user where email = ?');
        $statement->bindParam(1, $email, \PDO::PARAM_STR);
        $statement->execute();
        $user = $statement->fetch(\PDO::FETCH_OBJ);
        if (!$user) {
            return false;
        }

        if ($user->bcrypt_password_flag) {
            return password_verify($password, $user->password);
        }
        if (sha1($password) !== $user->password) { // old password, and wrong
            return false;
        }
        // old password, needs upgrade
        $insert = $this->pdo->prepare('update user set bcrypt_password_flag = 1, password = ? where email = ?');
        $password = password_hash($password, PASSWORD_BCRYPT); // must be here to prevent a pass by reference error
        $insert->bindParam(1, $password, \PDO::PARAM_STR);
        $insert->bindParam(2, $email, \PDO::PARAM_STR);
        $insert->execute();

        return true;
    }

    /**
     * @return array|bool
     */
    public function getUserDetails($emailOrId)
    {
        $sql = 'SELECT 
                    user.user_id, email, first_name, last_name, user_access.account_id, user_access.myfoot_role, group_concat(user_facility_restrictions.listing_avail_id) as restricted_to_facility_ids
                FROM user 
                LEFT JOIN user_access USING (user_id)
                LEFT JOIN user_facility_restrictions USING (user_id)
                ';
        if (is_numeric($emailOrId)) {
            $statement = $this->pdo->prepare($sql.' WHERE user.user_id = ?');
            $statement->bindParam(1, $emailOrId, \PDO::PARAM_INT);
        } else {
            $statement = $this->pdo->prepare($sql.' WHERE user.email = ?');
            $statement->bindParam(1, $emailOrId, \PDO::PARAM_STR);
        }
        $statement->bindParam(1, $emailOrId, \PDO::PARAM_STR);
        $statement->execute();
        $user = $statement->fetch(\PDO::FETCH_OBJ);
        if (!$user->user_id) {
            return false;
        }

        return [
            'user_id' => $user->user_id,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'account_id' => $user->account_id,
            'restricted_to_facility_ids' => $user->restricted_to_facility_ids,
            'myfoot_role' => $user->myfoot_role,
        ];
    }
}
