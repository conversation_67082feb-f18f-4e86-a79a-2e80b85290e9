<?php

namespace Sparefoot\Authorization\Controller;

use Sparefoot\Authorization\Service\OA2Service;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class TokenController extends AbstractController
{
    private $service;

    public function __construct(OA2Service $service)
    {
        $this->service = $service;
    }

    public function token()
    {
        return $this->service->tokenRequest();
    }
}
