<?php

namespace Sparefoot\Authorization\Controller;

use Sparefoot\Authorization\Service\OA2Service;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;

class ResourceController extends AbstractController
{
    private $service;

    public function __construct(OA2Service $service)
    {
        $this->service = $service;
    }

    public function resource(Request $request)
    {
        return $this->service->resourceRequest($request);
    }
}
