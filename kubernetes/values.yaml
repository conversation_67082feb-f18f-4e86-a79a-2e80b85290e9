env: &Env
    MYSQL_DSN: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_DSN'|format(sf_env)) }}
    MYSQL_USER: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_USER'|format(sf_env)) }}
    MYSQL_PASS: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_PASS'|format(sf_env)) }}
    AUTH_DSN: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_AUTH_DSN'|format(sf_env)) }}
    AUTH_USER: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_AUTH_USER'|format(sf_env)) }}
    AUTH_PASS: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_AUTH_PASS'|format(sf_env)) }}

nodeSelector: 
  app2.sparefoot.com/group: "true"

hostnames:
{% if sf_env == 'prod' %}
- auth.sparefoot.com
{% elif sf_env == 'stage' %}
- auth.sparefoot.extrameter.com
{% else %}
- auth.sparefoot.moreyard.com
{% endif %}

{% if sf_env == 'prod' %}
pdb:
  create: true
  minAvailable: 50%
{% endif %}