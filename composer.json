{"name": "sparefoot/authorization", "description": "Service for SpareFoot Authorization", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/sf_service_bundle.git"}], "require": {"php": "^8.2", "datadog/dd-trace": "0.97.0", "symfony/serializer": "5.4.*", "symfony/twig-bridge": "5.4.*", "bshaffer/oauth2-server-php": "^1.13", "bshaffer/oauth2-server-httpfoundation-bridge": "^1.7", "sparefoot/sf_service_bundle": "dev-php8", "symfony/string": "^5.4", "symfony/translation-contracts": "^2.5"}, "require-dev": {"phpunit/phpunit": "10.4.*", "symfony/browser-kit": "*", "friendsofphp/php-cs-fixer": "^3.22"}, "autoload": {"psr-4": {"Sparefoot\\Authorization\\": "src/"}}, "autoload-dev": {"psr-4": {"Sparefoot\\Authorization\\Tests\\": "tests/"}}, "scripts": {"post-update-cmd": ["bash vendor/sparefoot/sf_service_bundle/Resources/bin/setup.sh"], "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}, "config": {"allow-plugins": {"ocramius/package-versions": true, "php-http/discovery": true, "symfony/flex": true}}}