# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public:
            true # Allows optimizing the container by removing unused services; this also means
            # fetching services directly from the container via $container->get() won't work.
            # The best practice is to be explicit about your dependencies anyway.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    <PERSON><PERSON>foot\Authorization\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php,Health,Dao,TelephoneNumber.php,Throttler.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    Spa<PERSON>foot\Authorization\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    pdo.master:
        class: \PDO
        autowire: false
        arguments:
            - '%env(MYSQL_DSN)%'
            - '%env(MYSQL_USER)%'
            - '%env(MYSQL_PASS)%'

    pdo.auth:
        class: \PDO
        autowire: false
        arguments:
            - '%env(AUTH_DSN)%'
            - '%env(AUTH_USER)%'
            - '%env(AUTH_PASS)%'

    Sparefoot\Authorization\Service\OA2Service:
        arguments:
            $pdoAuth: '@pdo.auth'

    Sparefoot\Authorization\Service\UserCredentialsService:
        arguments:
            $pdoMaster: '@pdo.master'
