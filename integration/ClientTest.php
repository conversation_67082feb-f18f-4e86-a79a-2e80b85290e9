<?php

namespace Sparefoot\Authorization\Test\SDK;

/*
 * Created by IntelliJ IDEA.
 * User: mland
 * Date: 3/23/15
 * Time: 4:12 PM
 */
use Sparefoot\Authorization\SDK\Client;
use Sparefoot\Authorization\SDK\User;

class ClientTest extends \PHPUnit\Framework\TestCase
{
    public function testCanGetToken()
    {
        $authClient = Client::getInstance();
        $authToken = $authClient->getAuthTokenFromUserCredentials('<EMAIL>', 't00feraps');
        $this->assertTrue(strlen($authToken) > 40);
    }

    public function testCanGetUserFromToken()
    {
        $authClient = Client::getInstance();
        $authToken = $authClient->getAuthTokenFromUserCredentials('<EMAIL>', 't00feraps');
        $this->assertTrue(strlen($authToken) > 10);

        $authUser = $authClient->getUserFromAuthToken($authToken);
        $this->assertEquals('<EMAIL>', strtolower($authUser->getEmail()));
    }

    public function testGetDifferentToken()
    {
        $authClient = Client::getInstance();
        $authToken1 = $authClient->getAuthTokenFromUserCredentials('<EMAIL>', 't00feraps');

        $authToken2 = $authClient->getAuthTokenFromUserCredentials('<EMAIL>', 't00feraps');
        $this->assertNotEquals($authToken1, $authToken2);
    }

    public function testCanRenewToken()
    {
        $authClient = Client::getInstance();
        $authToken1 = $authClient->getAuthTokenFromUserCredentials('<EMAIL>', 't00feraps');

        $authToken2 = $authClient->refreshAuthToken($authToken1->getRefresh());
        $this->assertNotEquals($authToken1, $authToken2);
        $this->assertNotEmpty($authToken2->getRefresh());
        $this->assertNotEmpty($authToken2->getIdentifier());
        $this->assertNotEmpty($authToken2->getExpires());
        $this->assertNotEmpty($authToken2->getDomain());
        $this->assertNotEmpty($authToken2->getHost());
    }
}
